﻿using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service to read production data from PLC
    /// </summary>
    public class ProductionDataReader
    {
        private readonly PlcConnectionManager _plcManager;
        private readonly Dictionary<string, bool> _lastErrorStates = new Dictionary<string, bool>();
        public ProductionDataReader(PlcConnectionManager plcManager)
        {
            _plcManager = plcManager;
        }

        public event EventHandler<PlcErrorEventArgs> ErrorDetected;
        public event EventHandler<PlcErrorEventArgs> ErrorCleared;

        public class PlcErrorEventArgs : EventArgs
        {
            public string ErrorCode { get; set; }
            public string ErrorDescription { get; set; }
            public DateTime Timestamp { get; set; }
            public List<int> Stations { get; set; }
        }

        /// <summary>
        /// Read production data from PLC and create ProductionData object
        /// </summary>
        public async Task<ProductionData> ReadProductionDataFromPlcAsync(string station = "ALL")
        {
            try
            {
                var productionData = new ProductionData
                {
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    Station = station,
                    WorkShift = UserSession.CurrentShift?.Name ?? "Unknown",
                    CreatedBy = "System"
                };

                // Read data from PLC_MainLine
                if (_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    var plcService = _plcManager.GetPlcService("PLC_MainLine");
                    
                    // Read production quantities
                    var productOkResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                    if (productOkResult.IsSuccess)
                        productionData.Product_OK = Convert.ToInt32(productOkResult.Value);

                    var productNgResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                    if (productNgResult.IsSuccess)
                        productionData.Product_NG = Convert.ToInt32(productNgResult.Value);

                    var productTotalResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductTotal);
                    if (productTotalResult.IsSuccess)
                        productionData.Product_Total = Convert.ToInt32(productTotalResult.Value);

                    // Read completion time (average from stations)
                    float totalCompleteTime = 0;
                    int validStations = 0;
                    
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timecompletest{i}");
                    //    var timeResult = await plcService.ReadAsync(stationEnum);
                    //    if (timeResult.IsSuccess)
                    //    {
                    //        totalCompleteTime += Convert.ToSingle(timeResult.Value);
                    //        validStations++;
                    //    }
                    //}
                    
                    if (validStations > 0)
                        productionData.Time_Complete = totalCompleteTime / validStations;

                    // Read delay time (sum from stations)
                    int totalDelayTime = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timedelayst{i}");
                    //    var delayResult = await plcService.ReadAsync(stationEnum);
                    //    if (delayResult.IsSuccess)
                    //        totalDelayTime += Convert.ToInt32(delayResult.Value);
                    //}
                    productionData.Time_Delay = totalDelayTime;

                    // Read stop time (sum from stations)
                    int totalStopTime = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timestopst{i}");
                    //    var stopResult = await plcService.ReadAsync(stationEnum);
                    //    if (stopResult.IsSuccess)
                    //        totalStopTime += Convert.ToInt32(stopResult.Value);
                    //}
                    productionData.Time_Stop = totalStopTime;

                    // Read stop count (sum from stations)
                    int totalStopCount = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Numberstopst{i}");
                    //    var stopCountResult = await plcService.ReadAsync(stationEnum);
                    //    if (stopCountResult.IsSuccess)
                    //        totalStopCount += Convert.ToInt32(stopCountResult.Value);
                    //}
                    productionData.Number_Stop = totalStopCount;

                    // Read error codes
                    var errorCodes = _lastErrorStates
                        .Where(kvp => kvp.Value)
                        .Select(kvp => kvp.Key)
                        .ToList();
                    productionData.Error_Code = string.Join(", ", errorCodes);
                    productionData.Error_Text = GetErrorDescription(productionData.Error_Code);
                }

                return productionData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProductionDataReader: Error reading from PLC: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Read error bits from PLC and raise ErrorDetected event for each error
        /// </summary>
        public async Task ReadPlcErrorsAsync()
        {
            try
            {
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("ProductionDataReader: PLC_MainLine not connected");
                    return;
                }

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                var currentErrorCodes = new List<string>();

                for (int i = 0; i <= 90; i++)
                {
                    var errorEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"M40{i:D2}");
                    var errorCode = $"M40{i:D2}";
                    var errorResult = await plcService.ReadAsync(errorEnum);

                    if (errorResult.IsSuccess)
                    {
                        bool isErrorActive = Convert.ToBoolean(errorResult.Value);
                        bool wasErrorActive = _lastErrorStates.ContainsKey(errorCode) && _lastErrorStates[errorCode];

                        var (errorDescription, stations) = GetErrorDetails(errorCode);

                        if (isErrorActive && !wasErrorActive)
                        {
                            var errorArgs = new PlcErrorEventArgs
                            {
                                ErrorCode = errorCode,
                                ErrorDescription = errorDescription,
                                Timestamp = DateTime.Now,
                                Stations = stations
                            };
                            ErrorDetected?.Invoke(this, errorArgs);
                            currentErrorCodes.Add(errorCode);
                        }
                        else if (!isErrorActive && wasErrorActive)
                        {
                            var errorArgs = new PlcErrorEventArgs
                            {
                                ErrorCode = errorCode,
                                ErrorDescription = errorDescription,
                                Timestamp = DateTime.Now,
                                Stations = stations
                            };
                            ErrorCleared?.Invoke(this, errorArgs);
                        }

                        _lastErrorStates[errorCode] = isErrorActive;
                    }
                }

                Console.WriteLine($"ProductionDataReader: Found {currentErrorCodes.Count} errors: {string.Join(", ", currentErrorCodes)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProductionDataReader: Error reading PLC errors: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get optimized shift actual calculation
        /// Current shift actual = Total daily actual - Previous shifts actual (from database)
        /// </summary>
        public async Task<int> GetOptimizedShiftActualAsync(string shiftName, IDatabaseService databaseService)
        {
            try
            {
                // Get current total actual from PLC
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                    return 0;

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                var okResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                var ngResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                
                int currentTotalActual = 0;
                if (okResult.IsSuccess) currentTotalActual += Convert.ToInt32(okResult.Value);
                if (ngResult.IsSuccess) currentTotalActual += Convert.ToInt32(ngResult.Value);

                // Get previous shifts actual from database (today only)
                var previousShiftsActual = await databaseService.GetTodayPreviousShiftsActualAsync(shiftName);

                // Current shift actual = Total - Previous shifts
                int currentShiftActual = currentTotalActual - previousShiftsActual;
                
                Console.WriteLine($"ProductionDataReader: Optimized shift actual calculation: Total={currentTotalActual}, Previous={previousShiftsActual}, Current={currentShiftActual}");
                
                return Math.Max(0, currentShiftActual); // Ensure non-negative
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProductionDataReader: Error calculating optimized shift actual: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Error description based on error code from fault_mapping.json
        /// </summary>
        private string GetErrorDescription(string errorCode)
        {
            if (string.IsNullOrEmpty(errorCode))
                return "";

            try
            {
                var errorCodes = errorCode.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var descriptions = new List<string>();

                foreach (var code in errorCodes)
                {
                    var trimmedCode = code.Trim();
                    var (description, _) = GetErrorDetails(trimmedCode);
                    descriptions.Add(description);
                }

                return string.Join("; ", descriptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting error description: {ex.Message}");
                return $"Error description unavailable: {errorCode}";
            }
        }
        private (string Description, List<int> Stations) GetErrorDetails(string errorCode)
        {
            try
            {
                var faultMappingPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "fault_mapping.json");
                if (File.Exists(faultMappingPath))
                {
                    var jsonContent = File.ReadAllText(faultMappingPath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var faultMapping = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(jsonContent, options);

                    if (faultMapping != null && faultMapping.ContainsKey(errorCode))
                    {
                        var faultInfo = faultMapping[errorCode];
                        var message = faultInfo.ContainsKey("message") ? faultInfo["message"].ToString() : $"Unknown error: {errorCode}";
                        var stations = faultInfo.ContainsKey("stations")
                            ? JsonSerializer.Deserialize<List<int>>(faultInfo["stations"].ToString(), options) ?? new List<int>()
                            : new List<int>();
                        return (message, stations);
                    }
                }

                return ($"Error description unavailable: {errorCode}", new List<int>());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading fault mapping for {errorCode}: {ex.Message}");
                return ($"Error description unavailable: {errorCode}", new List<int>());
            }
        }
    }
}
