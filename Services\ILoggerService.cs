using System;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Interface for logging service
    /// </summary>
    public interface ILoggerService
    {
        /// <summary>
        /// Log an information message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Log a warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Log an error message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogError(string message);

        /// <summary>
        /// Log an error with exception details
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Exception to log</param>
        void LogError(string message, Exception exception);

        /// <summary>
        /// Log a debug message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogDebug(string message);
    }
}
