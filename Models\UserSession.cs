namespace ZoomableApp.Models
{
    public static class UserSession
    {
        public static User? CurrentUser { get; set; }
        public static WorkShift? CurrentShift { get; set; }
        public static DateTime LoginTime { get; set; }

        public static bool IsLoggedIn => CurrentUser != null;

        public static void Login(User user, WorkShift shift)
        {
            CurrentUser = user;
            CurrentShift = shift;
            LoginTime = DateTime.Now;
        }

        public static void Logout()
        {
            CurrentUser = null;
            CurrentShift = null;
            LoginTime = default;
        }

        public static string GetDisplayInfo()
        {
            if (!IsLoggedIn || CurrentShift == null)
                return "Chưa đăng nhập";

            return $"{CurrentUser!.Fullname}\n{CurrentShift.DisplayText}";
        }
    }
}
