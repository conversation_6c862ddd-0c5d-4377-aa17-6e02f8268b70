using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service xuất báo cáo Excel
    /// </summary>
    public class ReportExcelService : BaseExcelService
    {
        private readonly string _exportPath;

        public ReportExcelService()
        {
            // Lấy đường dẫn từ config, mặc định là E:\Project-Dat\PANA
            var config = ConfigLoader.LoadExcelSettings();
            _exportPath = Path.GetDirectoryName(config.PlanFilePath) ?? @"E:\Project-Dat\PANA";

            // Tạo thư mục nếu chưa tồn tại
            EnsureDirectoryExists(Path.Combine(_exportPath, "dummy.xlsx"));
        }

        /// <summary>
        /// Xuất báo cáo sản lượng
        /// </summary>
        public string ExportProductionReport(List<ProductionData> data, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("SanLuong");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = CreateWorkbook();
                var worksheet = GetOrCreateWorksheet(workbook, "Báo cáo sản lượng");

                // Header thông tin
                var titleRange = worksheet.Range(1, 1, 1, 12);
                titleRange.Cell(1, 1).Value = "BÁO CÁO SẢN LƯỢNG";
                ApplyTitleFormatting(titleRange);

                worksheet.Cell(2, 1).Value = $"Thời gian: {fromDate?.ToString("dd/MM/yyyy") ?? "Tất cả"} - {toDate?.ToString("dd/MM/yyyy") ?? "Tất cả"}";
                worksheet.Cell(3, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[]
                {
                    "STT", "Thời gian", "Trạm", "Sản phẩm OK", "Sản phẩm NG",
                    "Tổng sản phẩm", "Thời gian hoàn thành", "Thời gian trễ",
                    "Thời gian dừng", "Số lần dừng", "Ca làm việc", "Ghi chú"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = worksheet.Cell(5, i + 1);
                    cell.Value = headers[i];
                    ApplyHeaderFormatting(cell);
                }

                // Data
                for (int i = 0; i < data.Count; i++)
                {
                    var row = i + 6;
                    var item = data[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Product_OK;
                    worksheet.Cell(row, 5).Value = item.Product_NG;
                    worksheet.Cell(row, 6).Value = item.Product_Total;
                    worksheet.Cell(row, 7).Value = item.Time_Complete;
                    worksheet.Cell(row, 8).Value = item.Time_Delay;
                    worksheet.Cell(row, 9).Value = item.Time_Stop;
                    worksheet.Cell(row, 10).Value = item.Number_Stop;
                    worksheet.Cell(row, 11).Value = item.WorkShift;
                    worksheet.Cell(row, 12).Value = item.Notes;
                }

                // Auto-fit columns
                AutoFitColumns(worksheet);

                // Borders
                var dataRange = worksheet.Range(5, 1, 5 + data.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                SaveWorkbook(workbook, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting production report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo thao tác chậm
        /// </summary>
        public string ExportSlowOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("ThaoTacCham");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Thao tác chậm");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO THAO TÁC CHẬM";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 8).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Thời gian hoàn thành", "Thời gian trễ", "Ca làm việc", "Mã lỗi", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = headers[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(4, i + 1).Style.Fill.BackgroundColor = XLColor.Orange;
                }

                // Data - chỉ hiển thị các trạm có thao tác chậm
                var slowData = data.Where(d => d.Time_Delay > 0 || d.Time_Complete > 10).ToList();
                
                for (int i = 0; i < slowData.Count; i++)
                {
                    var row = i + 5;
                    var item = slowData[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Time_Complete;
                    worksheet.Cell(row, 5).Value = item.Time_Delay;
                    worksheet.Cell(row, 6).Value = item.WorkShift;
                    worksheet.Cell(row, 7).Value = item.Error_Code;
                    worksheet.Cell(row, 8).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(4, 1, 4 + slowData.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting slow operation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo đo thao tác
        /// </summary>
        public string ExportMeasureOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("DoThaoTac");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Đo thao tác");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO ĐO THAO TÁC";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 10).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";
                worksheet.Cell(3, 1).Value = "Đo 10 lần liên tiếp cho 18 vị trí làm việc";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Lần đo", "Thời gian hoàn thành", "Thời gian trễ", "Số lần dừng", "Ca làm việc", "Mã lỗi", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(5, i + 1).Value = headers[i];
                    worksheet.Cell(5, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(5, i + 1).Style.Fill.BackgroundColor = XLColor.LightBlue;
                }

                // Data
                for (int i = 0; i < data.Count; i++)
                {
                    var row = i + 6;
                    var item = data[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = (i % 10) + 1; // Lần đo (1-10)
                    worksheet.Cell(row, 5).Value = item.Time_Complete;
                    worksheet.Cell(row, 6).Value = item.Time_Delay;
                    worksheet.Cell(row, 7).Value = item.Number_Stop;
                    worksheet.Cell(row, 8).Value = item.WorkShift;
                    worksheet.Cell(row, 9).Value = item.Error_Code;
                    worksheet.Cell(row, 10).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(5, 1, 5 + data.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting measure operation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo tổng hợp tháng
        /// </summary>
        public string ExportMonthlyReport(List<ProductionData> data, int month, int year)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("TongHopThang");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Tổng hợp tháng");

                // Header
                worksheet.Cell(1, 1).Value = $"BÁO CÁO TỔNG HỢP THÁNG {month}/{year}";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 10).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Column headers
                var headers = new[] { "Ngày", "Ca", "Trạm", "Tổng SP", "SP OK", "SP NG", "Tỷ lệ OK (%)" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = headers[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                }

                // Data rows
                int row = 5;
                foreach (var item in data)
                {
                    worksheet.Cell(row, 1).Value = item.Timestamp;
                    worksheet.Cell(row, 2).Value = item.WorkShift;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Product_Total;
                    worksheet.Cell(row, 5).Value = item.Product_OK;
                    worksheet.Cell(row, 6).Value = item.Product_NG;

                    double okRate = item.Product_Total > 0 ? (double)item.Product_OK / item.Product_Total * 100 : 0;
                    worksheet.Cell(row, 7).Value = $"{okRate:F2}";

                    row++;
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                SaveWorkbook(workbook, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting monthly report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Fill monthly data into the MonthlyDataAnalytics Excel template
        /// </summary>
        public string FillMonthlyDataAnalytics(List<ProductionData> data, int month, int year)
        {
            try
            {
                // Get the template file path
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "excel", "MonthlyDataAnalytics.xlsx");

                // Create a copy of the template in the export directory
                string fileName = $"MonthlyDataAnalytics_{month:D2}_{year}.xlsx";
                string filePath = Path.Combine(_exportPath, fileName);

                // Make sure the template exists, create it if it doesn't
                if (!File.Exists(templatePath))
                {
                    Console.WriteLine($"Template file not found: {templatePath}. Creating new template...");
                    MonthlyDataAnalyticsTemplateCreator.CreateTemplate();

                    if (!File.Exists(templatePath))
                    {
                        throw new FileNotFoundException($"Could not create template file: {templatePath}");
                    }
                }

                // Copy the template to the destination
                File.Copy(templatePath, filePath, true);

                // Open the copied file
                using var workbook = OpenWorkbook(filePath);
                var worksheet = workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    throw new InvalidOperationException("No worksheet found in the template");
                }

                // Calculate monthly totals
                int totalOutput = data.Sum(d => d.Product_Total);
                int totalOK = data.Sum(d => d.Product_OK);
                int totalNG = data.Sum(d => d.Product_NG);

                // Calculate average stop time across all stations
                double avgStopTime = 0;
                var stationData = data.Where(d => !string.IsNullOrEmpty(d.Station) && d.Time_Stop > 0)
                                      .GroupBy(d => d.Station)
                                      .Select(g => new { Station = g.Key, AvgStopTime = g.Average(d => d.Time_Stop) });

                if (stationData.Any())
                {
                    avgStopTime = stationData.Average(s => s.AvgStopTime);
                }

                // Fill data vertically (dọc) without headers
                // Find the starting cell and fill data downward
                FillDataVertically(worksheet, new string[] {
                    totalOutput.ToString(),
                    totalOK.ToString(),
                    totalNG.ToString(),
                    $"{avgStopTime:F2}"
                });

                // Save the workbook
                SaveWorkbook(workbook, filePath);

                Console.WriteLine($"Monthly data analytics saved to {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error filling monthly data analytics: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Fill data vertically (dọc) starting from a specific cell
        /// </summary>
        private void FillDataVertically(IXLWorksheet worksheet, string[] values)
        {
            try
            {
                // Find the starting cell - look for any existing data cell or use default position
                IXLCell startCell = null;

                // Try to find a cell with "total" text first
                var totalCell = worksheet.CellsUsed()
                    .FirstOrDefault(c => c.Value.ToString().Equals("total", StringComparison.OrdinalIgnoreCase));

                if (totalCell != null)
                {
                    // Use the cell to the right of "total" as starting point
                    startCell = totalCell.CellRight();
                }
                else
                {
                    // Default to cell B5 if no reference found
                    startCell = worksheet.Cell(5, 2);
                }

                // Fill data vertically (downward)
                for (int i = 0; i < values.Length; i++)
                {
                    var targetCell = startCell.CellBelow(i);
                    targetCell.Value = values[i];
                    Console.WriteLine($"Filled cell {targetCell.Address} with value '{values[i]}'");
                }

                Console.WriteLine($"Successfully filled {values.Length} values vertically starting from {startCell.Address}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error filling data vertically: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Helper method to fill a named cell in the worksheet
        /// </summary>
        private void FillNamedCell(IXLWorksheet worksheet, string cellName, string value)
        {
            try
            {
                bool cellFilled = false;

                // Try to find the named range in the worksheet first
                var namedRange = worksheet.NamedRanges.FirstOrDefault(nr => nr.Name.Equals(cellName, StringComparison.OrdinalIgnoreCase));

                if (namedRange != null)
                {
                    // Fill the named range with the value
                    namedRange.Ranges.First().FirstCell().Value = value;
                    cellFilled = true;
                    Console.WriteLine($"Filled named range '{cellName}' with value '{value}'");
                }

                if (!cellFilled)
                {
                    // If named range not found, search for text in cells
                    var cellsWithText = worksheet.CellsUsed()
                        .Where(c => c.Value.ToString().Equals(cellName, StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    if (cellsWithText.Any())
                    {
                        foreach (var cell in cellsWithText)
                        {
                            // Fill the cell to the right of the found cell
                            cell.CellRight().Value = value;
                            cellFilled = true;
                            Console.WriteLine($"Found text '{cellName}' in cell {cell.Address} and filled adjacent cell with '{value}'");
                        }
                    }
                }

                if (!cellFilled)
                {
                    // If still not found, search for partial matches or add to a default location
                    Console.WriteLine($"Could not find cell or named range '{cellName}'. Searching for partial matches...");

                    var partialMatches = worksheet.CellsUsed()
                        .Where(c => c.Value.ToString().Contains(cellName, StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    if (partialMatches.Any())
                    {
                        foreach (var cell in partialMatches)
                        {
                            cell.CellRight().Value = value;
                            Console.WriteLine($"Found partial match '{cellName}' in cell {cell.Address} and filled adjacent cell with '{value}'");
                        }
                    }
                    else
                    {
                        // As a last resort, add to a default location
                        Console.WriteLine($"No matches found for '{cellName}'. Adding to default location.");
                        var lastRow = worksheet.LastRowUsed()?.RowNumber() ?? 1;
                        worksheet.Cell(lastRow + 1, 1).Value = cellName;
                        worksheet.Cell(lastRow + 1, 2).Value = value;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error filling named cell {cellName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xuất báo cáo lịch sử lỗi
        /// </summary>
        public string ExportErrorHistoryReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("LichSuLoi");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Lịch sử lỗi");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO LỊCH SỬ LỖI";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 8).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Mã lỗi", "Mô tả lỗi", "Ca làm việc", "Người tạo", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = headers[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(4, i + 1).Style.Fill.BackgroundColor = XLColor.LightCoral;
                }

                // Data - chỉ hiển thị các bản ghi có lỗi
                var errorData = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();
                
                for (int i = 0; i < errorData.Count; i++)
                {
                    var row = i + 5;
                    var item = errorData[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Error_Code;
                    worksheet.Cell(row, 5).Value = item.Error_Text;
                    worksheet.Cell(row, 6).Value = item.WorkShift;
                    worksheet.Cell(row, 7).Value = item.CreatedBy;
                    worksheet.Cell(row, 8).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(4, 1, 4 + errorData.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting error history report: {ex.Message}");
                throw;
            }
        }


    }
}
