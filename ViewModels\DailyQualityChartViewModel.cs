﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Daily Quality Chart (Panel 2 - Right)
    /// Hiển thị OK/NG/Rework ca hiện tại dưới dạng pie chart
    /// </summary>
    public class DailyQualityChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer? _refreshTimer;
        private string _lastUpdated = "";
        private ILoggerService? _logger;
        public event PropertyChangedEventHandler? PropertyChanged;
        public ObservableCollection<ISeries> Series { get; set; } = new();
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }
        private int _okQuantity;
        public int OkQuantity
        {
            get => _okQuantity;
            set
            {
                if (_okQuantity != value)
                {
                    _okQuantity = value;
                    OnPropertyChanged(nameof(OkQuantity));
                }
            }
        }
        private int _ngQuantity;
        public int NgQuantity
        {
            get => _ngQuantity;
            set
            {
                if (_ngQuantity != value)
                {
                    _ngQuantity = value;
                    OnPropertyChanged(nameof(NgQuantity));
                }
            }
        }
        private int _reworkQuantity;
        public int ReworkQuantity
        {
            get => _reworkQuantity;
            set
            {
                if (_reworkQuantity != value)
                {
                    _reworkQuantity = value;
                    OnPropertyChanged(nameof(ReworkQuantity));
                }
            }
        }
        private int _totalQuantity;
        public int TotalQuantity
        {
            get => _totalQuantity;
            set
            {
                if (_totalQuantity != value)
                {
                    _totalQuantity = value;
                    OnPropertyChanged(nameof(TotalQuantity));
                }
            }
        }

        private double _qualityRate;
        public double QualityRate
        {
            get => _qualityRate;
            set
            {
                if (_qualityRate != value)
                {
                    _qualityRate = value;
                    OnPropertyChanged(nameof(QualityRate));
                }
            }
        }
        public string CurrentDay { get; private set; } = "";
        public DailyQualityChartViewModel()
        {
            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
            }

            _mockDataService = new MockDashboardDataService();
            CurrentDay = DateTime.Now.ToString("dd/MM/yyyy");

            Task.Run(async () =>
            {
                await ServiceContainer.WaitForPlcConnectionAsync();
                _logger?.LogInfo("[DailyQualityChart] PLC connections ready, starting timer");
                InitializeTimer();
                await LoadDataAsync(); // Tải dữ liệu lần đầu sau khi kết nối
            });
        }
        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _logger?.LogInfo($"[DailyQualityChart] Refresh interval set to {interval} ms");
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) =>
            {
                var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (plcManager?.IsPlcConnected("PLC_MainLine") == true)
                {
                    _logger?.LogInfo("[DailyQualityChart] PLC_MainLine connected, loading data...");
                    await LoadDataAsync();
                }
                else
                {
                    _logger?.LogWarning("[DailyQualityChart] PLC_MainLine not connected, skipping data load");
                }
            };
            _refreshTimer.Start();
        }
        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();

                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // Load real data from PLC
                        LoadRealData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DailyQualityChart] Error loading data: {ex.Message}");
                    _logger?.LogError($"[DailyQualityChart] Error loading data: {ex.Message}", ex);
                    LoadMockData(); // Fallback
                }
            });
        }

        private void LoadRealData()
        {
            try
            {
                // Đọc từ PLC registers cho Real mode
                var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (plcManager != null)
                {
                    var plcService = plcManager.GetPlcService("PLC_MainLine");
                    if (plcService != null && plcService.IsConnected)
                    {
                        // Đọc dữ liệu quality từ PLC registers
                        var okResult = plcService.ReadAsync(PlcDeviceAddress.NumberProductOk).Result;
                        var ngResult = plcService.ReadAsync(PlcDeviceAddress.NumberProductNg).Result;

                        int okCount = 0, ngCount = 0, reworkCount = 0;

                        if (okResult.IsSuccess)
                        {
                            okCount = Convert.ToInt32(okResult.Value);
                        }

                        if (ngResult.IsSuccess)
                        {
                            ngCount = Convert.ToInt32(ngResult.Value);
                        }

                        // For now, rework count is 0 as we don't have a specific register for it
                        // You can add PlcDeviceAddress.NumberProductRework if available
                        reworkCount = 0;

                        OkQuantity = okCount;
                        NgQuantity = ngCount;
                        ReworkQuantity = reworkCount;
                        TotalQuantity = OkQuantity + NgQuantity + ReworkQuantity;
                        QualityRate = TotalQuantity > 0 ? Math.Round((double)OkQuantity / TotalQuantity * 100, 1) : 0;

                        UpdateChart();
                        _logger?.LogInfo($"[DailyQualityChart] Real data loaded - OK: {OkQuantity}, NG: {NgQuantity}, Rework: {ReworkQuantity}");
                    }
                    else
                    {
                        _logger?.LogWarning("[DailyQualityChart] PLC service not available, using mock data");
                        LoadMockData();
                    }
                }
                else
                {
                    _logger?.LogWarning("[DailyQualityChart] PLC manager not available, using mock data");
                    LoadMockData();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[DailyQualityChart] Error loading real data: {ex.Message}", ex);
                LoadMockData(); // Fallback to mock data
            }
        }
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void LoadMockData()
        {
            var qualityData = _mockDataService.GetMockTodayQualityData(); // Today data
            OkQuantity = qualityData.OK;
            NgQuantity = qualityData.NG;
            ReworkQuantity = qualityData.Rework;
            TotalQuantity = OkQuantity + NgQuantity + ReworkQuantity;
            QualityRate = TotalQuantity > 0 ? (double)OkQuantity / TotalQuantity * 100 : 0;

            UpdateChart();
        }

        private void UpdateChart()
        {
            if (Series.Count == 0)
            {
                // Tạo pie chart cho OK/NG/Rework
                if (OkQuantity > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { OkQuantity },
                        Name = $"OK ({OkQuantity})",
                        Fill = new SolidColorPaint(SKColors.Green),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsPaint = new SolidColorPaint(SKColors.White),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }

                if (NgQuantity > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { NgQuantity },
                        Name = $"NG ({NgQuantity})",
                        Fill = new SolidColorPaint(SKColors.Red),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsPaint = new SolidColorPaint(SKColors.White),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }

                if (ReworkQuantity > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { ReworkQuantity },
                        Name = $"Rework ({ReworkQuantity})",
                        Fill = new SolidColorPaint(SKColors.Orange),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsPaint = new SolidColorPaint(SKColors.White),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }
            }
            else
            {
                // Cập nhật giá trị của các series đã có
                foreach (var series in Series)
                {
                    if (series is PieSeries<int> pieSeries)
                    {
                        if (pieSeries.Name.Contains("OK"))
                        {
                            pieSeries.Values = new[] { OkQuantity };
                        }
                        else if (pieSeries.Name.Contains("NG"))
                        {
                            pieSeries.Values = new[] { NgQuantity };
                        }
                        else if (pieSeries.Name.Contains("Rework"))
                        {
                            pieSeries.Values = new[] { ReworkQuantity };
                        }
                    }
                }
            }
            // Update properties for binding
            OnPropertyChanged(nameof(OkQuantity));
            OnPropertyChanged(nameof(NgQuantity));
            OnPropertyChanged(nameof(ReworkQuantity));
            OnPropertyChanged(nameof(TotalQuantity));
            OnPropertyChanged(nameof(QualityRate));

            _logger?.LogInfo($"ShiftQuality: OK={OkQuantity}, NG={NgQuantity}, Rework={ReworkQuantity}, Rate={QualityRate:F1}%");
        }

    }
}
