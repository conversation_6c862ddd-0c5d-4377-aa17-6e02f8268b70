using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Daily Idle Hours Chart (Panel 3 - Left)
    /// Hiển thị thời gian nghỉ hàng ngày dưới dạng half pie chart
    /// </summary>
    public class DailyIdleHoursChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private readonly IdlePlanService _idlePlanService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();

        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        public double ActualIdleHours { get; private set; }  // Actual idle time from PLC
        public double PlanIdleHours { get; private set; }    // Plan idle time from Excel
        public double RemainingIdleHours { get; private set; } // Plan - Actual
        public double IdleUtilizationRate { get; set; } // (Actual / Plan) * 100

        public DailyIdleHoursChartViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            _idlePlanService = new IdlePlanService();
            InitializeTimer();
            //LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();

                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // Load real data from PLC
                        LoadPlcData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DailyIdleHoursChart: Error loading data: {ex.Message}");
                    LoadMockData(); // Fallback
                }
            });
        }

        private void LoadPlcData()
        {
            try
            {
                // Check if ServiceContainer is initialized
                if (!ServiceContainer.IsRegistered<PlcConnectionManager>())
                {
                    Console.WriteLine("DailyIdleHoursChart: PlcConnectionManager not registered, using mock data");
                    LoadMockData();
                    return;
                }

                var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (plcManager != null && plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    var plcService = plcManager.GetPlcService("PLC_MainLine");
                    if (plcService != null && plcService.IsConnected)
                    {
                        // Read actual idle time from PLC registers
                        var stopHourResult = plcService.ReadAsync(PlcDeviceAddress.TimeStopHour).Result;
                        var stopMinuteResult = plcService.ReadAsync(PlcDeviceAddress.TimeStopMinute).Result;
                        var stopSecondResult = plcService.ReadAsync(PlcDeviceAddress.TimeStopSecond).Result;

                        if (stopHourResult.IsSuccess && stopMinuteResult.IsSuccess && stopSecondResult.IsSuccess)
                        {
                            // Calculate actual idle time from PLC registers
                            var stopHours = Convert.ToDouble(stopHourResult.Value);
                            var stopMinutes = Convert.ToDouble(stopMinuteResult.Value);
                            var stopSeconds = Convert.ToDouble(stopSecondResult.Value);

                            // Convert to total hours
                            ActualIdleHours = stopHours + (stopMinutes / 60.0) + (stopSeconds / 3600.0);

                            // Load plan idle time from Excel using IdlePlanService
                            PlanIdleHours = _idlePlanService.GetDailyPlanIdleTime(DateTime.Now);

                            // Calculate remaining idle time (Plan - Actual)
                            RemainingIdleHours = Math.Max(0, PlanIdleHours - ActualIdleHours);

                            // Calculate idle utilization rate (Actual / Plan * 100)
                            IdleUtilizationRate = PlanIdleHours > 0 ? (ActualIdleHours / PlanIdleHours) * 100 : 0;

                            // Ensure values are reasonable
                            if (ActualIdleHours < 0) ActualIdleHours = 0;
                            if (PlanIdleHours < 0) PlanIdleHours = 0;
                            if (RemainingIdleHours < 0) RemainingIdleHours = 0;

                            UpdateChart();
                            Console.WriteLine($"DailyIdleHours: PLC Data - Actual: {ActualIdleHours:F1}h, Plan: {PlanIdleHours:F1}h, Remaining: {RemainingIdleHours:F1}h, Utilization: {IdleUtilizationRate:F1}%");
                            return;
                        }
                        else
                        {
                            Console.WriteLine("DailyIdleHoursChart: Failed to read PLC stop time registers");
                        }
                    }
                }

                Console.WriteLine("DailyIdleHoursChart: PLC not available, using mock data");
                //LoadMockData();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DailyIdleHoursChart: Error reading PLC data: {ex.Message}");
                //LoadMockData();
            }
        }

        private void LoadMockData()
        {
            var idleData = _mockDataService.GetMockIdleHoursData(true); // Daily data

            ActualIdleHours = idleData.IdleHours;
            PlanIdleHours = idleData.TotalHours; // Use total as plan
            RemainingIdleHours = Math.Max(0, PlanIdleHours - ActualIdleHours);
            IdleUtilizationRate = PlanIdleHours > 0 ? (ActualIdleHours / PlanIdleHours) * 100 : 0;

            UpdateChart();
            Console.WriteLine($"DailyIdleHours: Mock Data - Actual={ActualIdleHours:F1}h, Plan={PlanIdleHours:F1}h, Remaining={RemainingIdleHours:F1}h, Utilization={IdleUtilizationRate:F1}%");
        }

        private void UpdateChart()
        {
            if (Series.Count == 0)
            {
                // Show Actual Idle Time vs Remaining Idle Time (not working time)
                if (ActualIdleHours > 0)
                {
                    Series.Add(new PieSeries<double>
                    {
                        Values = new[] { IdleUtilizationRate },
                        Name = $"Đã dừng ({IdleUtilizationRate:F1}%)",
                        Fill = new SolidColorPaint(SKColors.Red),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsPaint = new SolidColorPaint(SKColors.White),
                        InnerRadius = 20,
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue:F1}%",
                    });
                }

                if (RemainingIdleHours > 0)
                {
                    Series.Add(new PieSeries<double>
                    {
                        Values = new[] { 100 - IdleUtilizationRate },
                        Name = $"Còn lại ({100 - IdleUtilizationRate:F1}%)",
                        Fill = new SolidColorPaint(SKColors.Green),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        DataLabelsPaint = new SolidColorPaint(SKColors.White),
                        DataLabelsSize = 12,
                        InnerRadius = 20,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue:F1}%"
                    });
                }

            }
            else
            {
                // Update giá trị PieSeries hiện tại thay vì xóa
                if (ActualIdleHours > 0)
                {
                    var actualSeries = (PieSeries<double>)Series[0];
                    actualSeries.Values = new[] { IdleUtilizationRate };
                    actualSeries.Name = $"Đã dừng ({IdleUtilizationRate:F1}%)";
                }

                if (RemainingIdleHours > 0 && Series.Count > 1)
                {
                    var remainingSeries = (PieSeries<double>)Series[1];
                    remainingSeries.Values = new[] { 100 - IdleUtilizationRate };
                    remainingSeries.Name = $"Còn lại ({100 - IdleUtilizationRate:F1}%)";
                }
            }
            // Update properties for binding
            OnPropertyChanged(nameof(ActualIdleHours));
            OnPropertyChanged(nameof(PlanIdleHours));
            OnPropertyChanged(nameof(RemainingIdleHours));
            OnPropertyChanged(nameof(IdleUtilizationRate));
        }
        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
