﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using ZoomableApp.Models;
using ZoomableApp.PLC;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Simple dependency injection container for managing service instances
    /// Supports both singleton and transient service lifetimes
    /// </summary>
    public static class ServiceContainer
    {
        private static readonly Dictionary<Type, object> _singletonServices = new();
        private static readonly Dictionary<Type, Func<object>> _transientFactories = new();
        private static bool _isInitialized = false;
        private static TaskCompletionSource<bool> _plcConnectionTaskCompletionSource;

        /// <summary>
        /// Initializes the service container with the specified PLC mode
        /// </summary>
        /// <param name="plcMode">PLC operating mode</param>
        public static void Initialize(PlcMode plcMode)
        {
            if (_isInitialized)
            {
                Console.WriteLine("ServiceContainer: Already initialized, skipping...");
                return;
            }

            Console.WriteLine($"ServiceContainer: Initializing with PLC mode: {plcMode}");

            try
            {
                _plcConnectionTaskCompletionSource = new TaskCompletionSource<bool>();
                // Clear existing services
                _singletonServices.Clear();
                _transientFactories.Clear();

                // Register core services (including logger)
                RegisterCoreServices(plcMode);

                // Get logger for subsequent logging
                var logger = GetService<ILoggerService>();

                // Register PLC-related services
                RegisterPlcServices(plcMode);

                // Register dashboard services
                RegisterDashboardServices();

                _isInitialized = true;
                logger.LogInfo("ServiceContainer: Initialization completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ServiceContainer: Initialization failed: {ex.Message}");
                throw;
            }
        }

        public static async Task WaitForPlcConnectionAsync()
        {
            await _plcConnectionTaskCompletionSource.Task;
        }

        /// <summary>
        /// Gets a singleton service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        public static T GetService<T>()
        {
            var serviceType = typeof(T);

            if (_singletonServices.TryGetValue(serviceType, out var service))
            {
                return (T)service;
            }

            if (_transientFactories.TryGetValue(serviceType, out var factory))
            {
                return (T)factory();
            }

            throw new InvalidOperationException($"Service of type {serviceType.Name} is not registered");
        }

        /// <summary>
        /// Checks if a service is registered
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>True if service is registered</returns>
        public static bool IsRegistered<T>()
        {
            var serviceType = typeof(T);
            return _singletonServices.ContainsKey(serviceType) || _transientFactories.ContainsKey(serviceType);
        }

        /// <summary>
        /// Gets whether the ServiceContainer has been initialized
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// Registers a singleton service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="instance">Service instance</param>
        public static void RegisterSingleton<T>(T instance)
        {
            _singletonServices[typeof(T)] = instance;
            var message = $"ServiceContainer: Registered singleton {typeof(T).Name}";
            Console.WriteLine(message);

            // Try to log to file if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogDebug(message);
            }
        }

        /// <summary>
        /// Registers a transient service factory
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="factory">Factory function</param>
        public static void RegisterTransient<T>(Func<T> factory)
        {
            _transientFactories[typeof(T)] = () => factory();
            var message = $"ServiceContainer: Registered transient {typeof(T).Name}";
            Console.WriteLine(message);

            // Try to log to file if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogDebug(message);
            }
        }

        /// <summary>
        /// Resets the container and clears all registrations
        /// </summary>
        public static void Reset()
        {
            // Log before clearing if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogInfo("ServiceContainer: Reset completed");
            }

            _singletonServices.Clear();
            _transientFactories.Clear();
            _isInitialized = false;
            Console.WriteLine("ServiceContainer: Reset completed");
        }

        private static void RegisterCoreServices(PlcMode plcMode)
        {
            // Register logging service first
            var logger = new FileLoggerService();
            RegisterSingleton<ILoggerService>(logger);
            logger.LogInfo("ServiceContainer: Logging service initialized");

            // Register centralized database service - ALL database operations go through this
            var databaseService = new DatabaseService();
            RegisterSingleton<IDatabaseService>(databaseService);
            logger.LogInfo("ServiceContainer: Database service initialized");

            // Register UserService with DatabaseService dependency
            var userService = new UserService(databaseService);
            RegisterSingleton<UserService>(userService);
            logger.LogInfo("ServiceContainer: User service initialized");

            // Register configuration-related services
            RegisterSingleton<PlcMode>(plcMode);

            // Register PLC connection manager with PlcMode
            var plcConfigs = ConfigLoader.LoadPlcConfigs();
            var plcManager = new PlcConnectionManager(plcConfigs, plcMode);
            RegisterSingleton<PlcConnectionManager>(plcManager);

            Task.Run(async () =>
            {
                await plcManager.ConnectAllAutoConnectPlcsAsync();
                _plcConnectionTaskCompletionSource.SetResult(true); // Báo hiệu kết nối xong
                logger.LogInfo("ServiceContainer: PLC connections established");
            });

            // Register PLC fault service
            var faultService = new PlcFaultService();
            RegisterSingleton<PlcFaultService>(faultService);

            var canvasFaultDisplayService = new CanvasFaultDisplayService();
            RegisterSingleton<CanvasFaultDisplayService>(canvasFaultDisplayService);
        }

        private static void RegisterPlcServices(PlcMode plcMode)
        {
            // Register PLC service factory as transient
            RegisterTransient<IPlcService>(() => 
            {
                // This will be called each time a new PLC service is needed
                // In practice, you might want to pass specific PLC ID
                return PlcServiceFactory.CreatePlcService("PLC_MainLine", plcMode);
            });
            // Register ProductionDataReader as singleton
            var productionDataReader = new ProductionDataReader(GetService<PlcConnectionManager>());
            RegisterSingleton<ProductionDataReader>(productionDataReader);
            // Register DataAggregatorService as singleton
            var dataAggregator = new DataAggregatorService();
            RegisterSingleton<DataAggregatorService>(dataAggregator);
        }

        private static void RegisterDashboardServices()
        {
            // Register dashboard data service with DatabaseService dependency
            var databaseService = GetService<IDatabaseService>();

            // Register mock dashboard data service
            var mockDashboardService = new MockDashboardDataService();
            RegisterSingleton<MockDashboardDataService>(mockDashboardService);

            // Register PlanViewModel for plan data access
            var planViewModel = new PlanViewModel();
            RegisterSingleton<PlanViewModel>(planViewModel);

            // Register data saving service with dependencies
            var plcManager = GetService<PlcConnectionManager>();
            var productionDataReader = new ProductionDataReader(plcManager);
            var dataSavingService = new DataSavingService(databaseService, productionDataReader);
            RegisterSingleton<DataSavingService>(dataSavingService);

            // Register other dashboard-related services as needed
        }

        /// <summary>
        /// Gets diagnostic information about registered services
        /// </summary>
        /// <returns>Dictionary of service types and their registration status</returns>
        public static Dictionary<string, string> GetDiagnosticInfo()
        {
            var info = new Dictionary<string, string>();
            
            foreach (var kvp in _singletonServices)
            {
                info[kvp.Key.Name] = "Singleton";
            }
            
            foreach (var kvp in _transientFactories)
            {
                info[kvp.Key.Name] = "Transient";
            }
            
            info["IsInitialized"] = _isInitialized.ToString();
            info["TotalServices"] = (_singletonServices.Count + _transientFactories.Count).ToString();
            
            return info;
        }
    }
}
