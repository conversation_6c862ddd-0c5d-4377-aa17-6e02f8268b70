using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Daily Plan vs Actual Chart (Panel 3 - Left)
    /// Hi<PERSON>n thị kế hoạch vs thực tế hàng ngày dưới dạng half pie chart
    /// </summary>
    public class DailyPlanActualChartViewModel : INotifyPropertyChanged
    {
        private readonly ILoggerService _loggerService;
        private readonly MockDashboardDataService _mockDataService;
        private readonly PlanViewModel _planViewModel;
        private readonly PlcConnectionManager _plcManager;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();
        
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        private int _planQuantity;
        public int PlanQuantity
        {
            get => _planQuantity;
            set
            {
                if (_planQuantity != value)
                {
                    _planQuantity = value;
                    OnPropertyChanged(nameof(PlanQuantity));
                }
            }
        }

        private int _actualQuantity;
        public int ActualQuantity
        {
            get => _actualQuantity;
            set
            {
                if (_actualQuantity != value)
                {
                    _actualQuantity = value;
                    OnPropertyChanged(nameof(ActualQuantity));
                }
            }
        }

        private int _gap;
        public int Gap
        {
            get => _gap;
            set
            {
                if (_gap != value)
                {
                    _gap = value;
                    OnPropertyChanged(nameof(Gap));
                }
            }
        }

        private double _achievementRate;
        public double AchievementRate
        {
            get => _achievementRate;
            set
            {
                if (_achievementRate != value)
                {
                    _achievementRate = value;
                    OnPropertyChanged(nameof(AchievementRate));
                }
            }
        }

        public string CurrentDay { get; private set; } = "";

        public DailyPlanActualChartViewModel(PlanViewModel planViewModel = null, PlcConnectionManager plcManager = null)
        {
            _mockDataService = new MockDashboardDataService();
            _planViewModel = planViewModel;
            _plcManager = plcManager;
            CurrentDay = DateTime.Now.ToString("dd/MM/yyyy");

            // Debug logging
            Console.WriteLine($"DailyPlanActualChart: Constructor - PlanViewModel: {(_planViewModel != null ? "Available" : "NULL")}");
            Console.WriteLine($"DailyPlanActualChart: Constructor - PlcManager: {(_plcManager != null ? "Available" : "NULL")}");
            if (_planViewModel != null)
            {
                Console.WriteLine($"DailyPlanActualChart: Constructor - DailyPlanData: {(_planViewModel.DailyPlanData != null ? $"{_planViewModel.DailyPlanData.Rows.Count} rows" : "NULL")}");

                // Log available columns for debugging
                if (_planViewModel.DailyPlanData != null)
                {
                    Console.WriteLine("DailyPlanActualChart: Available columns in DailyPlanData:");
                    foreach (System.Data.DataColumn column in _planViewModel.DailyPlanData.Columns)
                    {
                        Console.WriteLine($"  - '{column.ColumnName}' ({column.DataType.Name})");
                    }
                }
            }

            // Subscribe to PlanViewModel property changes to detect when data is loaded
            if (_planViewModel != null)
            {
                _planViewModel.PropertyChanged += PlanViewModel_PropertyChanged;
            }

            InitializeTimer();
            //LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        /// <summary>
        /// Handle PlanViewModel property changes to reload data when plan data becomes available
        /// </summary>
        private void PlanViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlanViewModel.DailyPlanData))
            {
                Console.WriteLine("DailyPlanActualChart: PlanViewModel.DailyPlanData changed, reloading data...");
                _ = Task.Run(async () => await LoadDataAsync());
            }
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(async () =>
            {
                try
                {
                    // Load real data from PlanViewModel and PLC
                    await LoadRealDataAsync();
                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DailyPlanActualChart: Error loading real data: {ex.Message}");
                    // Fallback to mock data
                    LoadMockData();
                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
            });
        }

        /// <summary>
        /// Load real data from PlanViewModel and PLC
        /// </summary>
        private async Task LoadRealDataAsync()
        {
            // Get plan data from PlanViewModel
            int planQuantity = GetPlanQuantityFromPlanViewModel();

            // Get actual data from PLC
            int actualQuantity = await GetActualQuantityFromPlcAsync();

            // Update properties
            PlanQuantity = planQuantity;
            ActualQuantity = actualQuantity;
            Gap = actualQuantity - planQuantity;
            AchievementRate = planQuantity > 0 ? Math.Round((double)actualQuantity / planQuantity * 100, 1) : 0;

            // Update chart
            UpdateChart();

            Console.WriteLine($"DailyPlanActualChart: Real Data - Plan={planQuantity}, Actual={actualQuantity}, Gap={Gap}, Rate={AchievementRate:F1}%");
        }

        /// <summary>
        /// Get plan quantity from PlanViewModel daily data
        /// </summary>
        private int GetPlanQuantityFromPlanViewModel()
        {
            try
            {
                if (_planViewModel?.DailyPlanData == null || _planViewModel.DailyPlanData.Rows.Count == 0)
                {
                    Console.WriteLine("DailyPlanActualChart: No daily plan data available");
                    return 0;
                }

                // Look for quantity columns in the daily plan data
                var dailyData = _planViewModel.DailyPlanData;
                int totalPlanQuantity = 0;

                // First, let's log all available columns for debugging
                Console.WriteLine("DailyPlanActualChart: Available columns:");
                foreach (DataColumn column in dailyData.Columns)
                {
                    Console.WriteLine($"  - {column.ColumnName}");
                }

                foreach (DataRow row in dailyData.Rows)
                {
                    // Look for the correct quantity column "Q'ty"
                    foreach (DataColumn column in dailyData.Columns)
                    {
                        var columnName = column.ColumnName;
                        if (columnName == "Q'ty" || columnName.ToLower() == "qty" ||
                            columnName.ToLower() == "quantity" || columnName.Contains("Q'ty"))
                        {
                            if (int.TryParse(row[column].ToString(), out int quantity))
                            {
                                totalPlanQuantity += quantity;
                            }
                            else
                            {
                                Console.WriteLine($"DailyPlanActualChart: Could not parse quantity from column '{column.ColumnName}', value: '{row[column]}'");
                            }
                        }
                    }
                }
                return totalPlanQuantity;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DailyPlanActualChart: Error getting plan quantity: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Get actual quantity from PLC (similar to UpdateSystemStatusDisplay method)
        /// </summary>
        private async Task<int> GetActualQuantityFromPlcAsync()
        {
            try
            {
                if (_plcManager == null || !_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("DailyPlanActualChart: PLC_MainLine not connected, returning 0");
                    return 0;
                }

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null)
                {
                    Console.WriteLine("DailyPlanActualChart: PLC service not available");
                    return 0;
                }

                // Read total production (OK + NG) from PLC
                int totalOK = 0;
                int totalNG = 0;

                // Read OK products
                var okResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                if (okResult.IsSuccess)
                {
                    totalOK = Convert.ToInt32(okResult.Value);
                }

                // Read NG products
                var ngResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                if (ngResult.IsSuccess)
                {
                    totalNG = Convert.ToInt32(ngResult.Value);
                }

                int totalActual = totalOK + totalNG;
                Console.WriteLine($"DailyPlanActualChart: PLC Data - OK={totalOK}, NG={totalNG}, Total={totalActual}");

                return totalActual;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DailyPlanActualChart: Error reading from PLC: {ex.Message}");
                _loggerService?.LogError($"DailyPlanActualChart: Error reading from PLC: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Update chart with current data
        /// </summary>
        private void UpdateChart()
        {
            if (Series.Count == 0)
            {
                // Create half pie chart for Plan vs Actual
                if (ActualQuantity <= PlanQuantity)
                {
                    // Actual doesn't exceed plan
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { ActualQuantity },
                        Name = $"Thực tế ({ActualQuantity})",
                        Fill = new SolidColorPaint(SKColors.LightGreen),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });

                    var remaining = PlanQuantity - ActualQuantity;
                    if (remaining > 0)
                    {
                        Series.Add(new PieSeries<int>
                        {
                            Values = new[] { remaining },
                            Name = $"Còn lại ({remaining})",
                            Fill = new SolidColorPaint(SKColors.LightGray),
                            Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                            InnerRadius = 40,
                            DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                            DataLabelsSize = 12,
                            DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                            DataLabelsFormatter = point => $"{point.PrimaryValue}"
                        });
                    }
                }
                else
                {
                    // Actual exceeds plan
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { PlanQuantity },
                        Name = $"Kế hoạch ({PlanQuantity})",
                        Fill = new SolidColorPaint(SKColors.LightBlue),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });

                    var excess = ActualQuantity - PlanQuantity;
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { excess },
                        Name = $"Vượt KH (+{excess})",
                        Fill = new SolidColorPaint(SKColors.Gold),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"+{point.PrimaryValue}"
                    });
                }
            }
            else
            {
                if (ActualQuantity <= PlanQuantity)
                {
                    var actualSeries = (PieSeries<int>)Series[0];
                    actualSeries.Values = new[] { ActualQuantity };
                    actualSeries.Name = $"Thực tế ({ActualQuantity})";
                    if (Series.Count > 1)
                    {
                        var remainingSeries = (PieSeries<int>)Series[1];
                        remainingSeries.Values = new[] { PlanQuantity - ActualQuantity };
                        remainingSeries.Name = $"Còn lại ({PlanQuantity - ActualQuantity})";
                    }
                }
                // Actual exceeds plan
                else
                {
                    var planSeries = (PieSeries<int>)Series[0];
                    planSeries.Values = new[] { PlanQuantity };
                    planSeries.Name = $"Kế hoạch ({PlanQuantity})";
                    if (Series.Count > 1)
                    {
                        var excessSeries = (PieSeries<int>)Series[1];
                        excessSeries.Values = new[] { ActualQuantity - PlanQuantity };
                        excessSeries.Name = $"Vượt KH (+{ActualQuantity - PlanQuantity})";
                    }
                }
            }
        }

        private void LoadMockData()
        {
            var planData = _mockDataService.GetMockPlanActualData(false); // Daily data

            PlanQuantity = planData.Plan;
            ActualQuantity = planData.Actual;
            Gap = planData.Gap;
            AchievementRate = PlanQuantity > 0 ? Math.Round((double)ActualQuantity / PlanQuantity * 100, 1) : 0;

            // Use the same chart update method for consistency
            UpdateChart();

            Console.WriteLine($"DailyPlanActualChart: Mock Data - Plan={PlanQuantity}, Actual={ActualQuantity}, Gap={Gap}, Rate={AchievementRate:F1}%");
        }

        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
