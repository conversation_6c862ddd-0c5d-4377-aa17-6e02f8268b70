using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using ZoomableApp.Models;

namespace ZoomableApp.SharedControls
{
    /// <summary>
    /// Control for displaying fault information on canvas layouts
    /// </summary>
    public partial class FaultDisplayControl : UserControl
    {
        private PlcFaultInfo? _faultInfo;
        private Storyboard? _blinkAnimation;

        public PlcFaultInfo? FaultInfo
        {
            get => _faultInfo;
            set
            {
                _faultInfo = value;
                UpdateDisplay();
            }
        }

        public FaultDisplayControl()
        {
            InitializeComponent();
            CreateBlinkAnimation();
        }

        private void UpdateDisplay()
        {
            if (_faultInfo == null || !_faultInfo.IsActive)
            {
                Visibility = Visibility.Collapsed;
                _blinkAnimation?.Stop();
                return;
            }

            Visibility = Visibility.Visible;
            FaultCodeText.Text = $"LỖI: {_faultInfo.FaultCode}";
            FaultMessageText.Text = _faultInfo.Message;
            FaultStationsText.Text = _faultInfo.StationsText;

            // Start blinking animation for critical faults
            if (IsCriticalFault(_faultInfo.FaultCode))
            {
                _blinkAnimation?.Begin();
            }
            else
            {
                _blinkAnimation?.Stop();
                FaultBorder.Opacity = 0.9;
            }
        }

        private bool IsCriticalFault(string faultCode)
        {
            // Safety-related faults are critical
            return faultCode.StartsWith("S") || faultCode.StartsWith("E");
        }

        private void CreateBlinkAnimation()
        {
            _blinkAnimation = new Storyboard();
            _blinkAnimation.RepeatBehavior = RepeatBehavior.Forever;

            var opacityAnimation = new DoubleAnimation
            {
                From = 0.9,
                To = 0.3,
                Duration = TimeSpan.FromMilliseconds(500),
                AutoReverse = true
            };

            Storyboard.SetTarget(opacityAnimation, FaultBorder);
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));
            _blinkAnimation.Children.Add(opacityAnimation);
        }

        /// <summary>
        /// Position this control at specific coordinates on canvas
        /// </summary>
        public void SetCanvasPosition(double x, double y)
        {
            Canvas.SetLeft(this, x);
            Canvas.SetTop(this, y);
        }

        /// <summary>
        /// Position this control above a specific station
        /// </summary>
        public void PositionAboveStation(int stationNumber, double stationX, double stationWidth)
        {
            // Position above the station center
            double x = stationX + (stationWidth / 2) - (ActualWidth / 2);
            double y = -70; // Above the station
            
            SetCanvasPosition(x, y);
        }
    }
}
