namespace ZoomableApp.Models
{
    /// <summary>
    /// Defines the operating mode for PLC services
    /// </summary>
    public enum PlcMode
    {
        /// <summary>
        /// Use real PLC connections (production mode)
        /// </summary>
        Real,
        
        /// <summary>
        /// Use mock/simulated PLC data (testing/demo mode)
        /// </summary>
        Mock
    }

    /// <summary>
    /// PLC device addresses for reading/writing data
    /// </summary>
    public enum PlcDeviceAddress
    {
        NumberProductOk, // Số sản phẩm OK
        NumberProductNg, // Số sản phẩm NG
        NumberProductTotal, // Tổng số sản phẩm
        NumProductPresent,
        Timemodel, // Thời gian model
        Timecurrent, // Thời gian đếm hiện tại
        Timedelay, // Thời gian trễ hiện tại
        TimeNextStep, // Thời gian chuyển bước
        Timecompletest1, // Thời gian hoàn thành công đoạn tại các trạm
        Timecompletest2,
        Timecompletest3,
        Timecompletest4,
        Timecompletest5,
        Timecompletest6,
        Timecompletest7,
        Timecompletest8,
        Timecompletest9,
        Timecompletest10,
        Timecompletest11,
        Timecompletest12,
        Timecompletest13,
        Timecompletest14,
        Timecompletest15,
        Timecompletest16,
        Timecompletest17,
        Timecompletest18,
        Timecompletest19,
        Timecompletest20,
        Timecompletest21,
        Timecompletest22,
        Timecompletest23,
        Timecompletest24,
        Timecompletest25,
        Timecompletest26,
        Timedelayst1, // Thời gian trễ của các trạm ( gần nhất)
        Timedelayst2,
        Timedelayst3,
        Timedelayst4,
        Timedelayst5,
        Timedelayst6,
        Timedelayst7,
        Timedelayst8,
        Timedelayst9,
        Timedelayst10,
        Timedelayst11,
        Timedelayst12,
        Timedelayst13,
        Timedelayst14,
        Timedelayst15,
        Timedelayst16,
        Timedelayst17,
        Timedelayst18,
        Timedelayst19,
        Timedelayst20,
        Timedelayst21,
        Timedelayst22,
        Timedelayst23,
        Timedelayst24,
        Timedelayst25,
        Timedelayst26,
        Timestopst1, // Tổng thời gian dừng của các trạm
        Timestopst2,
        Timestopst3,
        Timestopst4,
        Timestopst5,
        Timestopst6,
        Timestopst7,
        Timestopst8,
        Timestopst9,
        Timestopst10,
        Timestopst11,
        Timestopst12,
        Timestopst13,
        Timestopst14,
        Timestopst15,
        Timestopst16,
        Timestopst17,
        Timestopst18,
        Timestopst19,
        Timestopst20,
        Timestopst21,
        Timestopst22,
        Timestopst23,
        Timestopst24,
        Timestopst25,
        Timestopst26,
        Numberstopst1, // Số lần dừng của các trạm
        Numberstopst2,
        Numberstopst3,
        Numberstopst4,
        Numberstopst5,
        Numberstopst6,
        Numberstopst7,
        Numberstopst8,
        Numberstopst9,
        Numberstopst10,
        Numberstopst11,
        Numberstopst12,
        Numberstopst13,
        Numberstopst14,
        Numberstopst15,
        Numberstopst16,
        Numberstopst17,
        Numberstopst18,
        Numberstopst19,
        Numberstopst20,
        Numberstopst21,
        Numberstopst22,
        Numberstopst23,
        Numberstopst24,
        Numberstopst25,
        Numberstopst26,
        TimeRunningHour, // Thời gian chạy H
        TimeRunningMinute, // Thời gian chạy M
        TimeRunningSecond, // Thời gian chạy S
        TimeStopHour, // Thời gian dừng  H
        TimeStopMinute, // Thời gian dừng  M
        TimeStopSecond, // Thời gian dừng  S
        NotError, // (=1 không lỗi, =0 có lỗi)
        Automode, // Chế độ tự động
        Manmode, // Chế độ bằng tay
        Originmode, // Trạng thái về gốc
        Homedone, // Về gốc xong
        Runningmode, // Trạng thái đang chạy tự dộng
        Stopmode, // Trạng thái dừng
        M4000, // M4000: Lỗi biến tần băng tải ST2
        M4001, // M4001: Lỗi biến tần băng tải ST3-ST6
        M4002, // M4002: Lỗi biến tần băng tải hồi ST3-ST6
        M4003, // M4003: Lỗi biến tần băng tải ST7-ST8
        M4004, // M4004: Lỗi biến tần băng tải ST9-ST10
        M4005, // M4005: Lỗi biến tần băng tải hồi ST7-ST10
        M4006, // M4006: Lỗi biến tần băng tải ST11-ST14
        M4007, // M4007: Lỗi biến tần băng tải hồi ST11-ST14
        M4008, // M4008: Lỗi biến tần băng tải ST15-ST18
        M4009, // M4009: Lỗi biến tần băng tải hồi ST15-ST18
        M4010, // M4010: Lỗi biến tần băng tải ST19-ST22
        M4011, // M4011: Lỗi biến tần băng tải hồi ST19-ST22
        M4012, // M4012: Lỗi biến tần băng tải ST23-ST26
        M4013, // M4013: Lỗi biến tần băng tải hồi ST23-ST26
        M4014, // M4014: Lỗi con lăn băng tải cụm nâng
        M4015, // M4015: Lỗi con lăn  Rework In
        M4016, // M4016: Lỗi con lăn băng tải Rework In
        M4017, // M4017: Lỗi con lăn  Rework Out
        M4018, // M4018: Lỗi cụm nâng nâng lên
        M4019, // M4019: Lỗi cụm nâng hạ xuống
        M4020, // M4020: Lỗi STP 2.1 lên
        M4021, // M4021: Lỗi STP 2.1 xuống
        M4022, // M4022: Lỗi STP 2.2 lên
        M4023, // M4023: Lỗi STP 2.2 xuống
        M4024, // M4024: Lỗi STP ST6 lên
        M4025, // M4025: Lỗi STP ST6 xuống
        M4026, // M4026: Lỗi STP hồi  ST2 lên
        M4027, // M4027: Lỗi STP hồi  ST2 xuống
        M4028, // M4028: Lỗi STP hồi  ST3 lên
        M4029, // M4029: Lỗi STP hồi  ST3 xuống
        M4030, // M4030:
        M4031, // M4031: Lỗi bàn xoay ST7 lên
        M4032, // M4032: Lỗi bàn xoay ST7 xuống
        M4033, // M4033: Lỗi STP ST10 lên
        M4034, // M4034: Lỗi STP ST10 xuống
        M4035, // M4035: Lỗi STP hồi  ST7 lên
        M4036, // M4036: Lỗi STP hồi  ST7 xuống
        M4037, // M4037: Lỗi STP ST7 lên
        M4038, // M4038: Lỗi STP ST7 xuống
        M4039, // M4039:
        M4040, // M4040: Lỗi STP ST14 lên
        M4041, // M4041: Lỗi STP ST14 xuống
        M4042, // M4042: Lỗi STP hồi  ST11 lên
        M4043, // M4043: Lỗi STP hồi  ST11 xuống
        M4044, // M4044:
        M4045, // M4045: Lỗi STP ST18 lên
        M4046, // M4046: Lỗi STP ST18 xuống
        M4047, // M4047: Lỗi STP hồi  ST15 lên
        M4048, // M4048: Lỗi STP hồi  ST15 xuống
        M4049, // M4049:
        M4050, // M4050: Lỗi STP ST22 lên
        M4051, // M4051: Lỗi STP ST22 xuống
        M4052, // M4052: Lỗi STP hồi  ST19 lên
        M4053, // M4053: Lỗi STP hồi  ST19 xuống
        M4054, // M4054: Lỗi STP hồi  ST20 lên
        M4055, // M4055: Lỗi STP hồi  ST20 xuống
        M4056, // M4056:
        M4057, // M4057: Lỗi Rework In  lên
        M4058, // M4058: Lỗi Rework In  xuống
        M4059, // M4059:
        M4060, // M4060: Lỗi Rework Out  lên
        M4061, // M4061: Lỗi Rework Out  xuống
        M4062, // M4062:
        M4063, // M4063: Lỗi STP ST26 lên
        M4064, // M4064: Lỗi STP ST26 xuống
        M4065, // M4065: Lỗi STP hồi  ST23 lên
        M4066, // M4066: Lỗi STP hồi  ST23 xuống
        M4067, // M4067:
        M4068, // M4068: Lỗi EMG tủ điện chính
        M4069, // M4069: Lỗi EMG cụm nâng
        M4070, // M4070: Lỗi EMG ST2-6
        M4071, // M4071: Lỗi EMG ST7-10
        M4072, // M4072: Lỗi EMG ST11-14
        M4073, // M4073: Lỗi EMG ST15-18
        M4074, // M4074: Lỗi EMG ST19-22
        M4075, // M4075: Lỗi EMG ST23-26
        M4076, // M4076: Lỗi EMG giật dây bên trái số 1
        M4077, // M4077: Lỗi EMG giật dây bên phải
        M4078, // M4078:
        M4079, // M4079:
        M4080, // M4080: Lỗi EMG cụm Rework
        M4081, // M4081: Cảnh báo tác động cảm biến vùng cụm nâng hạ
        M4082, // M4082:
        M4083, // M4083: Cảnh báo cụm nâng chưa sẵn sàng chuyển bước
        M4084, // M4084: Cảnh báo ST2 chưa sẵn sàng chuyển bước
        M4085, // M4085: Cảnh báo ST3-6 chưa sẵn sàng chuyển bước
        M4086, // M4086: Cảnh báo ST7-8 chưa sẵn sàng chuyển bước
        M4087, // M4087: Cảnh báo ST9-18 chưa sẵn sàng chuyển bước
        M4088, // M4088: Cảnh báo ST19-22 chưa sẵn sàng chuyển bước
        M4089, // M4089: Lỗi EMG giật dây bên trái số 2
        M4090, // M4090: Lỗi EMG giật dây bên trái số 3
        MoveTrigger, // Bit kích hoạt di chuyển sản phẩm
        CardAccess, // Bit kích hoạt thẻ truy cập

        //---- Draft cho Inspection ----
        ProductCode_ReaderStation, // Mã sản phẩm đọc từ trạm
        CurrentProductSequenceNumberWord, // Số thứ tự sản phẩm hiện tại (WORD)

        //---- PLC8_Tester registers ----
        TestResultFromTester, // Kết quả test từ máy test
        SequenceNumberAtTest, // Số thứ tự tại máy test
        VoltageFromTester, // Điện áp từ máy test
    }
    /// <summary>
    /// PLC data types for reading/writing
    /// </summary>
    public enum PlcDataType
    {
        BIT,
        WORD,       // 16-bit signed/unsigned
        DWORD,      // 32-bit signed/unsigned
        FLOAT,      // Single-precision float
        STRING      // Chuỗi ký tự, thường lưu trong nhiều WORD
    }

    /// <summary>
    /// Station types in the production line
    /// </summary>
    public enum StationType
    {
        None,           // Mặc định
        Lifter,
        Crane,          // Cầu trục
        WorkerRoller,   // Con lăn cho công nhân làm việc
        TestRoller,     // Con lăn máy test
        StandardRoller, // Con lăn tiêu chuẩn
        ReworkRoller    // Con lăn có nhánh rework
    }

    /// <summary>
    /// Test result status for stations
    /// </summary>
    public enum TestResultStatus
    {
        None,           // Không có thông tin/Không áp dụng
        AwaitingProduct,// Trạm test đang chờ sản phẩm
        Testing,        // Đang trong quá trình test
        OK,
        NG
    }

    /// <summary>
    /// Device operational status
    /// </summary>
    public enum DeviceOperationalStatus
    {
        None,           // Không có thông tin
        Idle,           // Đang rảnh, sẵn sàng
        Running,        // Đang hoạt động
        Error,          // Lỗi thiết bị
        Off,            // Tắt
        Maintenance     // Đang bảo trì
    }

    /// <summary>
    /// Plan item status for daily plans
    /// </summary>
    public enum PlanItemStatus
    {
        NotStarted,     // Chưa bắt đầu - màu nền bình thường
        Selected,       // Được chọn - màu xanh dương  
        InProgress,     // Đang làm - màu vàng
        Completed       // Hoàn thành - màu xanh lá
    }

    /// <summary>
    /// Report types for production data
    /// </summary>
    public enum ReportType
    {
        Production,         // Sản lượng
        SlowOperation,      // Thao tác chậm
        MeasureOperation,   // Đo thao tác
        MonthlyReport,      // Tổng hợp tháng
        ErrorHistory        // Lịch sử lỗi
    }
}
