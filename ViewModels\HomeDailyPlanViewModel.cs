using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    public class HomeDailyPlanViewModel : INotifyPropertyChanged
    {
        private readonly PlanViewModel _planViewModel;
        private readonly ILoggerService _logger;
        private readonly DataAggregatorService _dataAggregator;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems;
        private DailyPlanItem _selectedItem;
        private DailyPlanItem _currentWorkingItem;
        private string _todayDateText = "";
        private string _currentProductText = "";
        private string _editableModelName = "";
        private bool _isEditingModel = false;

        public HomeDailyPlanViewModel(PlanViewModel planViewModel, DataAggregatorService dataAggregator = null)
        {
            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger.LogInfo("[HomeVM] CONSTRUCTOR: ViewModel is being created.");
            }
            else
            {
                Console.WriteLine("[HomeVM] CONSTRUCTOR: ViewModel is being created.");
            }

            _planViewModel = planViewModel ?? throw new ArgumentNullException(nameof(planViewModel));
            _dataAggregator = dataAggregator;
            _dailyPlanItems = new ObservableCollection<DailyPlanItem>();

            // Subscribe to DataAggregator changes if available
            if (_dataAggregator != null)
            {
                _dataAggregator.PropertyChanged += DataAggregator_PropertyChanged;
            }

            // Initialize commands
            RefreshCommand = new RelayCommand(LoadDailyPlan);
            MarkCompleteCommand = new RelayCommand(MarkCurrentComplete, CanMarkComplete);
            StartNextCommand = new RelayCommand(StartSelectedItem, CanStartNext);
            StartEditModelCommand = new RelayCommand(StartEditModel);
            SaveModelCommand = new RelayCommand(SaveModel, CanSaveModel);
            CancelEditModelCommand = new RelayCommand(CancelEditModel);

            // Đăng ký lắng nghe sự kiện
            _planViewModel.PropertyChanged += PlanViewModel_PropertyChanged;

            Console.WriteLine("[HomeVM] CONSTRUCTOR: Calling RefreshData to get initial data...");
            RefreshData();
        }

        private void RefreshData()
        {
            try
            {
                Console.WriteLine("[HomeVM] RefreshData STARTING. Reading data from PlanViewModel...");
                var dailyData = _planViewModel.DailyPlanData;

                if (dailyData == null)
                {
                    Console.WriteLine("[HomeVM] RefreshData: PlanViewModel.DailyPlanData is NULL. Clearing items.");
                    DailyPlanItems.Clear();
                    return;
                }

                DailyPlanItems.Clear();
                foreach (DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "", // Chú ý ký tự '
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    DailyPlanItems.Add(item);
                }

                Console.WriteLine($"[HomeVM] RefreshData FINISHED. Added {DailyPlanItems.Count} items.");

                // === SỬA LOGIC CẬP NHẬT SẢN PHẨM HIỆN TẠI ===
                if (DailyPlanItems.Any())
                {
                    // Lấy item đầu tiên làm sản phẩm đang làm việc
                    CurrentWorkingItem = DailyPlanItems.First();
                    CurrentWorkingItem.Status = PlanItemStatus.InProgress;

                    // Sử dụng UpdateCurrentProductText để áp dụng logic ưu tiên PLC data
                    UpdateCurrentProductText();
                    Console.WriteLine($"[HomeVM] Current product set to: '{CurrentProductText}'");
                }
                else
                {
                    CurrentWorkingItem = null;
                    UpdateCurrentProductText();
                }
                Console.WriteLine($"[HomeVM] RefreshData FINISHED. Added {DailyPlanItems.Count} items to its own collection.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[HomeVM] RefreshData ERROR: {ex.Message}");
            }
        }

        private void PlanViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlanViewModel.DailyPlanData))
            {
                Console.WriteLine("[HomeVM] EVENT HANDLER: Received notification that PlanViewModel.DailyPlanData changed. Calling RefreshData...");
                RefreshData();
            }
        }

        #region Properties

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                _dailyPlanItems = value;
                OnPropertyChanged();
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public DailyPlanItem CurrentWorkingItem
        {
            get => _currentWorkingItem;
            set
            {
                _currentWorkingItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                _todayDateText = value;
                OnPropertyChanged();
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                _currentProductText = value;
                OnPropertyChanged();
            }
        }

        public string EditableModelName
        {
            get => _editableModelName;
            set
            {
                _editableModelName = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditingModel
        {
            get => _isEditingModel;
            set
            {
                _isEditingModel = value;
                OnPropertyChanged();
            }
        }

        public ICommand RefreshCommand { get; private set; }
        public ICommand StartEditModelCommand { get; private set; }
        public ICommand SaveModelCommand { get; private set; }
        public ICommand CancelEditModelCommand { get; private set; }
        public ICommand MarkCompleteCommand { get; private set; }
        public ICommand StartNextCommand { get; private set; }

        #endregion

        #region Methods

        private void LoadDailyPlan()
        {
            try
            {
                Console.WriteLine("HomeDailyPlan: LoadDailyPlan called - refreshing PlanViewModel");

                // Refresh PlanViewModel data first
                if (_planViewModel.RefreshCommand.CanExecute(null))
                {
                    _planViewModel.RefreshCommand.Execute(null);
                }

                // Then load from PlanViewModel
                LoadDailyPlanFromPlanViewModel();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"HomeDailyPlan: Error in LoadDailyPlan: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }

        private void LoadDailyPlanFromPlanViewModel()
        {
            try
            {
                Console.WriteLine("HomeDailyPlan: LoadDailyPlanFromPlanViewModel called");

                var dailyData = _planViewModel.DailyPlanData;
                if (dailyData == null || dailyData.Rows.Count == 0)
                {
                    Console.WriteLine("HomeDailyPlan: No daily data available from PlanViewModel");
                    DailyPlanItems.Clear();
                    CurrentWorkingItem = null;
                    UpdateCurrentProductText();
                    return;
                }

                // Debug: Print column names
                Console.WriteLine("HomeDailyPlan: Available columns:");
                foreach (DataColumn column in dailyData.Columns)
                {
                    Console.WriteLine($"  - '{column.ColumnName}'");
                }
                
                DailyPlanItems.Clear();
                
                foreach (DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    
                    DailyPlanItems.Add(item);
                }

                // Update current product if we have items
                if (DailyPlanItems.Count > 0)
                {
                    // Set first item as current working if no current item
                    if (CurrentWorkingItem == null)
                    {
                        CurrentWorkingItem = DailyPlanItems.First();
                        CurrentWorkingItem.Status = PlanItemStatus.InProgress;
                    }

                    // Use UpdateCurrentProductText to apply PLC priority logic
                    UpdateCurrentProductText();
                }
                else
                {
                    CurrentWorkingItem = null;
                    UpdateCurrentProductText();
                }

                Console.WriteLine($"HomeDailyPlan: Loaded {DailyPlanItems.Count} items from PlanViewModel daily data");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"HomeDailyPlan: Error loading daily plan from PlanViewModel: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }



        private void UpdateCurrentProductText()
        {
            // Prioritize PLC data if available and not empty
            if (_dataAggregator != null && !string.IsNullOrWhiteSpace(_dataAggregator.CurrentProductCode))
            {
                CurrentProductText = _dataAggregator.CurrentProductCode;
                Console.WriteLine($"[HomeVM] Using PLC product code: '{CurrentProductText}'");
            }
            else if (CurrentWorkingItem != null)
            {
                CurrentProductText = CurrentWorkingItem.ModelName;
                Console.WriteLine($"[HomeVM] Using plan model name: '{CurrentProductText}'");
            }
            else
            {
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void DataAggregator_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(DataAggregatorService.CurrentProductCode))
            {
                Console.WriteLine($"[HomeVM] DataAggregator product code changed, updating display");
                UpdateCurrentProductText();
            }
        }

        private void MarkCurrentComplete()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentWorkingItem.Status = PlanItemStatus.Completed;
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }

        private bool CanMarkComplete()
        {
            return CurrentWorkingItem != null && CurrentWorkingItem.Status == PlanItemStatus.InProgress;
        }

        private void StartSelectedItem()
        {
            if (SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted)
            {
                SelectedItem.Status = PlanItemStatus.InProgress;
                CurrentWorkingItem = SelectedItem;
                SelectedItem = null;
            }
        }

        private bool CanStartNext()
        {
            return SelectedItem != null && 
                   SelectedItem.Status == PlanItemStatus.NotStarted && 
                   CurrentWorkingItem == null;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void StartEditModel()
        {
            _logger?.LogInfo("[HomeVM] StartEditModel called - preparing to edit model name.");
            EditableModelName = CurrentProductText;
            IsEditingModel = true;
        }

        private void SaveModel()
        {
            if (!string.IsNullOrWhiteSpace(EditableModelName))
            {
                // Update the current working item if it exists
                if (CurrentWorkingItem != null)
                {
                    CurrentWorkingItem.ModelName = EditableModelName.Trim();
                }

                // Use UpdateCurrentProductText to apply proper priority logic
                UpdateCurrentProductText();

                IsEditingModel = false;
                Console.WriteLine($"[HomeVM] Model name updated to: '{CurrentProductText}'");
            }
        }

        private bool CanSaveModel()
        {
            return IsEditingModel && !string.IsNullOrWhiteSpace(EditableModelName);
        }

        private void CancelEditModel()
        {
            EditableModelName = "";
            IsEditingModel = false;
        }

        #endregion
    }

    public class DailyPlanItem : INotifyPropertyChanged
    {
        private PlanItemStatus _status;

        public string No { get; set; } = "";
        public string Type { get; set; } = "";
        public string ModelName { get; set; } = "";
        public string Market { get; set; } = "";
        public string Quantity { get; set; } = "";
        public string StartTime { get; set; } = "";
        public string StopTime { get; set; } = "";

        public PlanItemStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
