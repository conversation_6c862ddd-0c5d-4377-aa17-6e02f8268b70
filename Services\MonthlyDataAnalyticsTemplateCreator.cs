using System;
using System.IO;
using ClosedXML.Excel;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service to create the MonthlyDataAnalytics Excel template
    /// </summary>
    public class MonthlyDataAnalyticsTemplateCreator
    {
        /// <summary>
        /// Create the MonthlyDataAnalytics Excel template with named cells
        /// </summary>
        public static void CreateTemplate()
        {
            try
            {
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "excel", "MonthlyDataAnalytics.xlsx");
                
                // Ensure the excel directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(templatePath));
                
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Monthly Analytics");
                
                // Set up simple template structure without headers
                // Just create a reference cell for positioning
                worksheet.Cell(5, 1).Value = "total"; // Reference cell to find starting position

                // Create placeholder cells for vertical data (will be filled by FillDataVertically)
                worksheet.Cell(5, 2).Value = "0"; // total
                worksheet.Cell(6, 2).Value = "0"; // totalOK
                worksheet.Cell(7, 2).Value = "0"; // totalNG
                worksheet.Cell(8, 2).Value = "0.00"; // AvgStopTime

                // Format the data column
                worksheet.Column(2).Width = 15;
                
                // Save the template
                workbook.SaveAs(templatePath);
                
                Console.WriteLine($"MonthlyDataAnalytics template created at: {templatePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating MonthlyDataAnalytics template: {ex.Message}");
                throw;
            }
        }
    }
}
