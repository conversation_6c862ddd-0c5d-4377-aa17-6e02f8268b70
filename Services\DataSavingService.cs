using System;
using System.Threading.Tasks;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service for handling all production data saving operations
    /// </summary>
    public class DataSavingService
    {
        private readonly IDatabaseService _databaseService;
        private readonly ProductionDataReader _productionDataReader;

        public DataSavingService(IDatabaseService databaseService, ProductionDataReader productionDataReader)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _productionDataReader = productionDataReader ?? throw new ArgumentNullException(nameof(productionDataReader));
        }

        /// <summary>
        /// Save production data on user login
        /// </summary>
        public async Task SaveProductionDataOnLoginAsync()
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = $"User login at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on login - {productionData.ReportType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on login: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data when new shift starts
        /// </summary>
        public async Task SaveProductionDataOnNewShiftStartAsync()
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = $"New shift started at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on shift start - {productionData.ReportType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on shift start: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data when error is detected
        /// </summary>
        /// <param name="productionData">Production data with error information</param>
        public async Task SaveProductionDataOnErrorAsync(ProductionData productionData)
        {
            try
            {
                productionData.ReportType = "ErrorDetected";
                productionData.Notes = $"Error detected: {productionData.Error_Code} at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on error - {productionData.Error_Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on error: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data on hourly schedule
        /// </summary>
        public async Task SaveProductionDataOnHourlyScheduleAsync()
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = $"Hourly scheduled save at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on hourly schedule - {productionData.ReportType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on hourly schedule: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data before shift change (5 minutes before)
        /// </summary>
        public async Task SaveProductionDataBeforeShiftChangeAsync()
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = $"Pre-shift change save at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved before shift change - {productionData.ReportType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data before shift change: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data when station changes
        /// </summary>
        /// <param name="stationNumber">Station number that changed</param>
        public async Task SaveProductionDataOnStationChangeAsync(string stationNumber)
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = $"Station {stationNumber} change at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on station change - Station {stationNumber}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on station change: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data when report type changes
        /// </summary>
        /// <param name="newReportType">New report type</param>
        public async Task SaveProductionDataOnReportTypeChangeAsync(string newReportType)
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = newReportType;
                productionData.Notes = $"Report type changed to {newReportType} at {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Production data saved on report type change - {newReportType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving production data on report type change: {ex.Message}");
            }
        }

        /// <summary>
        /// Save production data with custom report type and notes
        /// </summary>
        /// <param name="reportType">Custom report type</param>
        /// <param name="notes">Custom notes</param>
        public async Task SaveProductionDataCustomAsync(string reportType, string notes)
        {
            try
            {
                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = reportType;
                productionData.Notes = notes;

                await _databaseService.SaveProductionDataAsync(productionData);
                Console.WriteLine($"DataSavingService: Custom production data saved - {reportType}: {notes}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DataSavingService: Error saving custom production data: {ex.Message}");
            }
        }
    }
}
